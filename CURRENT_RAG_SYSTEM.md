# Current RAG System - Production Ready

## Overview

The current RAG (Retrieval-Augmented Generation) system is a **production-ready, brochure-optimized** implementation located in the `docqa/` folder. This is the **latest and best** RAG strategy that should be used throughout the application.

## Architecture

### Core Components

1. **Production Integration** (`docqa/production_integration.py`)
   - Main entry point for RAG functionality
   - Provides `BrochureRAGSystem` class
   - Singleton instance: `production_rag`

2. **Brochure-Optimized RAG** (`docqa/brochure_rag_system.py`)
   - Specialized for company brochure content
   - Advanced text normalization for marketing content
   - Metadata extraction for company information
   - Section-aware chunking

3. **Brochure QA System** (`docqa/brochure_qa_system.py`)
   - Optimized prompts for brochure questions
   - Lower similarity thresholds for marketing content
   - Enhanced answer generation

4. **Production Vector Store** (`docqa/vector_store/production_vector_store.py`)
   - pgvector-based storage
   - 1536-dimension embeddings
   - Proper similarity calculation
   - Metadata support

5. **Production Embeddings** (`docqa/vector_store/production_embeddings.py`)
   - OpenAI text-embedding-3-small
   - Retry logic and error handling
   - Consistent 1536-dimension vectors

6. **Production Text Processing** (`docqa/text_processing/production_text_processor.py`)
   - Advanced text normalization
   - Recursive character splitting
   - Meaningful text validation

## Key Features

### Brochure Optimization
- **Section Detection**: Automatically identifies headers, services, about, contact sections
- **Metadata Extraction**: Company name, industry, services, contact info
- **Marketing-Aware**: Preserves promotional language and key features
- **Lower Thresholds**: Optimized similarity thresholds (0.4) for brochure content

### Production Quality
- **Error Handling**: Comprehensive try/catch with proper logging
- **Retry Logic**: Automatic retries for API calls
- **Validation**: Input validation and dimension checking
- **Performance**: Optimized chunking and retrieval

### Integration Points
- **FastAPI Endpoints**: `/api/v1/docqa/ask` and `/api/v1/docqa/franchisor/{id}/ask`
- **Webhook Integration**: SMS and messaging webhook support
- **Agent System**: Compatible with the agents framework
- **CLI Support**: Command-line interface available

## Usage

### Python API
```python
from docqa import production_rag, ask_brochure_question

# Direct RAG system usage
rag_system = production_rag
answer = await rag_system.answer_brochure_question(
    question="What services does the company offer?",
    franchisor_id="uuid-here",
    similarity_threshold=0.4,
    top_k=5
)

# Simplified function usage
answer = await ask_brochure_question(
    question="What is the company about?",
    franchisor_id="uuid-here"
)
```

### FastAPI Endpoints
- `POST /api/v1/docqa/ask` - General questions across all documents
- `POST /api/v1/docqa/franchisor/{franchisor_id}/ask` - Franchisor-specific questions

### Webhook Integration
- Automatically integrated with Kudosity SMS webhooks
- Provides AI-powered answers to incoming questions
- Uses brochure-optimized RAG for better franchise information

## Database Schema

### Franchisors Table
- `embedding` column stores 1536-dimension vectors
- Used for franchisor brochure content
- Supports similarity search and retrieval

### Vector Storage
- pgvector extension for PostgreSQL
- Cosine similarity calculations
- Metadata storage for source tracking

## Configuration

### Environment Variables
- `OPENAI_API_KEY` - OpenAI API access
- `DATABASE_URL` - PostgreSQL connection with pgvector
- `EMBEDDING_MODEL` - text-embedding-3-small (default)

### Parameters
- **Chunk Size**: 350 tokens (optimized for brochures)
- **Chunk Overlap**: 50 tokens
- **Similarity Threshold**: 0.4 (lower for marketing content)
- **Top-K**: 5 chunks per query
- **Temperature**: 0.2 (balanced for marketing content)

## Removed Components

The following outdated RAG implementations have been **removed** to maintain a clean codebase:

- `qna_rag.py` - Older universal QnA system
- `brochure_rag_system.py` (root level) - Duplicate implementation
- `production_rag_system.py` - Older production system
- `integrate_production_rag.py` - Integration scripts
- `integrate_brochure_rag.py` - Integration scripts
- Various demo and test files for old systems

## Best Practices

1. **Use the docqa module**: Always import from `docqa` package
2. **Brochure-first**: Use brochure-optimized functions for franchise content
3. **Error handling**: Wrap RAG calls in try/catch blocks
4. **Logging**: Use structured logging for debugging
5. **Testing**: Test with actual franchise brochure content

## Maintenance

- **Single Source of Truth**: All RAG functionality is in `docqa/`
- **Production Ready**: No experimental or demo code
- **Well Tested**: Comprehensive test coverage
- **Documented**: Clear API documentation and examples

This is the **only RAG system** that should be used going forward. All other implementations have been removed to prevent confusion and maintain code quality.
